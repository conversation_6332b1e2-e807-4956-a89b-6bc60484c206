import {
  customProvider,
  extractReasoningMiddleware,
  wrapLanguageModel,
} from 'ai';

// import { createOpenAI } from "@ai-sdk/openai";
// import { createGoogleGenerativeAI } from "@ai-sdk/google";
// import { createDeepSeek } from "@ai-sdk/deepseek";
// import { checkReasoningMiddleware } from "./middleware";
import { createOpenRouter } from '@openrouter/ai-sdk-provider';
import { env } from '@/lib/env';

// const openai = createOpenAI({
//   apiKey: process.env.OPENAI_API_KEY!,
// });

// const deepseek = createDeepSeek({
//   apiKey: process.env.DEEPSEEK_API_KEY!,
// });

// const google = createGoogleGenerativeAI({
//   apiKey: process.env.GOOGLE_API_KEY!,
// });

const openrouter = createOpenRouter({
  apiKey: env.OPENROUTER_API_KEY,
  headers: {
    'HTTP-Referer': 'https://creator.jellyotter.ai/',
    'X-Title': 'JellyOtter',
  },
  extraBody: { include_reasoning: true },
});

export const supportTools = (modelId: modelID) => {
  if (
    // modelId === "DeepSeek-R1" ||
    modelId === 'Grok-3-mini'
  ) {
    return false;
  }

  return true;
};

const languageModels = {
  // "GPT-4o-mini": openai("gpt-4o-mini-2024-07-18"),
  // "GPT-4o": openai("gpt-4o-2024-11-20"),
  // "GPT-4.1": openai("gpt-4.1-2025-04-14"),
  // "GPT-4.1-mini": openai("gpt-4.1-mini-2025-04-14"),
  // "GPT-4.1-nano": openai("gpt-4.1-nano-2025-04-14"),
  // o3: wrapLanguageModel({
  //   middleware: extractReasoningMiddleware({
  //     tagName: "think",
  //   }),
  //   model: openrouter("openai/o3"),
  // }),
  // "o4-mini": wrapLanguageModel({
  //   middleware: extractReasoningMiddleware({
  //     tagName: "think",
  //   }),
  //   model: openrouter("openai/o4-mini"),
  // }),
  // "o4-mini-high": wrapLanguageModel({
  //   middleware: extractReasoningMiddleware({
  //     tagName: "think",
  //   }),
  //   model: openrouter("openai/o4-mini-high"),
  // }),
  //   "DeepSeek-R1": wrapLanguageModel({
  //     middleware: extractReasoningMiddleware({
  //       tagName: "think",
  //     }),
  //     model: deepseek("deepseek-reasoner"),
  //   }),
  //   "Deepseek-v3": deepseek("deepseek-chat"),
  'GPT-4o-mini': openrouter('openai/gpt-4o-mini'),
  'GPT-4o': openrouter('openai/gpt-4o'),
  'GPT-4.1': openrouter('openai/gpt-4.1'),
  'GPT-4.1-mini': openrouter('openai/gpt-4.1-mini'),
  'GPT-4.1-nano': openrouter('openai/gpt-4.1-nano'),
  'o4-mini': wrapLanguageModel({
    middleware: extractReasoningMiddleware({
      tagName: '',
    }),
    model: openrouter('openai/o4-mini', {
      reasoning: {
        exclude: false,
        effort: 'low',
      },
    }),
  }),
  'o4-mini-high': wrapLanguageModel({
    middleware: extractReasoningMiddleware({
      tagName: '',
    }),
    model: openrouter('openai/o4-mini-high', {
      includeReasoning: true,
      reasoning: {
        exclude: false,
        effort: 'high',
      },
    }),
  }),
  // "Gemini-2.0-flash": google("gemini-2.0-flash-001"),
  // "Gemini-2.5-flash": wrapLanguageModel({
  //   middleware: [],
  //   model: google("gemini-2.5-flash-preview-04-17"),
  // }),
  // "Gemini-2.5-pro": wrapLanguageModel({
  //   middleware: [],
  //   model: google("gemini-2.5-pro-preview-03-25"),
  // }),
  'Gemini-2.0-flash': openrouter('google/gemini-2.0-flash-001'),
  // 'Gemini-2.5-flash': openrouter('google/gemini-2.5-flash-preview-04-17'),
  'Gemini-2.5-flash': openrouter('google/gemini-2.5-flash-preview-05-20'),
  'Gemini-2.5-flash-thinking': wrapLanguageModel({
    middleware: [],
    model: openrouter('google/gemini-2.5-flash-preview:thinking', {
      reasoning: {
        exclude: false,
        effort: 'low',
      },
    }),
  }),
  'Gemini-2.5-pro': wrapLanguageModel({
    middleware: [],
    model: openrouter('google/gemini-2.5-pro-preview', {
      reasoning: {
        exclude: false,
        effort: 'low',
      },
    }),
  }),
  'Claude-Sonnet-3.5': openrouter('anthropic/claude-3.5-sonnet'),
  'Claude-Sonnet-3.7': openrouter('anthropic/claude-3.7-sonnet'),
  'Claude-Sonnet-3.7-thinking': wrapLanguageModel({
    middleware: [],
    model: openrouter('anthropic/claude-3.7-sonnet:thinking', {
      reasoning: {
        exclude: false,
        effort: 'low',
      },
    }),
  }),
  'Claude-Sonnet-4': openrouter('anthropic/claude-sonnet-4'),
  'Grok-3': wrapLanguageModel({
    middleware: [],
    model: openrouter('x-ai/grok-3-beta', {
      reasoning: {
        exclude: false,
        effort: 'low',
      },
    }),
  }),
  // Grok 3 mini not supporting tools
  'Grok-3-mini': wrapLanguageModel({
    middleware: [],
    model: openrouter('x-ai/grok-3-mini-beta', {
      reasoning: {
        exclude: false,
        effort: 'low',
      },
    }),
  }),
};

export const model = customProvider({
  languageModels,
});

export type modelID = keyof typeof languageModels;

export const MODELS = Object.keys(languageModels);

export const defaultModel: modelID = 'Gemini-2.5-flash';

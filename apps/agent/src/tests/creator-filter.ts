import {
  creatorFilterAgent,
  CreatorFilterOutputSchema,
} from '@/agents/creatorFilterAgent';
import { <PERSON><PERSON> } from '@mastra/core';

// Register the workflow
const mastra = new Mastra({
  agents: { creatorFilterAgent },
});

(async () => {
  const agent = mastra.getAgent('creatorFilterAgent');
  const resp = await agent.generate(
    [
      {
        role: 'user',
        content: `Filter Mode: STRICT

Creator Requirements: 1. 视频不同的游戏出现，并非只是同一个游戏。 2. 最近的视频播放量中位数大于 5 万。3. 要讲英语。4. 露脸讲话，有口播。

Scout Guidance: 优先选择视频中出现不同游戏的博主，这些视频的播放量中位数要大于 5 万。同时，这些博主的视频中要有口播，最好是露脸讲话的。`,
      },
      {
        role: 'user',
        content: [
          {
            type: 'image',
            image: new URL(
              'https://fastly.picsum.photos/id/237/200/300.jpg?hmac=TmmQSbShHz9CdQm0NkEjx1Dyh_Y984R9LpNrpvH2D_U',
            ),
          },
          {
            type: 'text',
            text: 'Random text',
          },
        ],
      },
    ],
    {
      output: CreatorFilterOutputSchema,
    },
  );

  console.log('resp', resp);
})();

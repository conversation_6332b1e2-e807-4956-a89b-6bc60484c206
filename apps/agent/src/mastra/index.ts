import { <PERSON><PERSON> } from '@mastra/core';
import { createLogger } from '@mastra/core/logger';
import { PostgresStore } from '@mastra/pg';
import { env } from '@/lib/env';
import { campaignAnalyzer } from '@/agents/campaignAnalyzerAgent';
import { WorkflowNames } from '@repo/constants';
import { creatorHashtagScout } from '@/agents/creatorHashtagScout';
import { creatorFilterAgent } from '@/agents/creatorFilterAgent';
import { challengePickerAgent } from '@/agents/challengePickerAgent';
import { creatorScoutWorkflow } from '@/workflows/creatorScoutWorkflow';

export const storage = new PostgresStore({
  connectionString: env.DB_POSTGRES_URL,
});

export const mastra = new Mastra({
  agents: {
    campaignAnalyzer,
    creatorHashtagScout,
    creatorFilterAgent,
    challengePickerAgent,
  },
  workflows: {
    // [WorkflowNames.aiScriptWorkflow]: aiScriptWorkflow,
    [WorkflowNames.creatorScoutWorkflow]: creatorScoutWorkflow,
  },
  logger: createLogger({
    name: 'JOA_DEV',
    level: 'info',
  }),
  vectors: {},
  storage: storage,
});
